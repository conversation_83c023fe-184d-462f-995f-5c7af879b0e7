use std::time::Duration;
use serde_json::json;

use secure_password_daemon::native_messaging::*;
use secure_password_daemon::ipc::{IpcClient, ClientConfig};

/// 基础集成测试
#[tokio::test]
async fn test_native_messaging_basic_integration() {
    // 测试协议编解码
    let codec = protocol::ProtocolCodec::new(protocol::ProtocolVersion::V2);
    
    let original_message = NativeMessage::new_request(
        "test_request_001".to_string(),
        json!({
            "action": "get_credentials",
            "domain": "example.com"
        }),
        "test_extension_id".to_string(),
    );
    
    // 编码和解码
    let encoded = codec.encode(&original_message).expect("编码失败");
    let decoded = codec.decode(&encoded).expect("解码失败");
    
    // 验证结果
    assert_eq!(decoded.request_id, original_message.request_id);
    assert_eq!(decoded.payload, original_message.payload);
    assert_eq!(decoded.source, original_message.source);
    
    println!("✅ 协议编解码测试通过");
}

/// 测试浏览器适配器
#[tokio::test]
async fn test_browser_adapters() {
    // 创建Chrome适配器
    let chrome_config = browser::BrowserAdapterConfig::default();
    let chrome_adapter = browser::ChromeAdapter::new(chrome_config);
    assert_eq!(chrome_adapter.browser_type(), browser::BrowserType::Chrome);
    
    // 创建Firefox适配器
    let firefox_config = browser::BrowserAdapterConfig {
        browser_type: browser::BrowserType::Firefox,
        ..Default::default()
    };
    let firefox_adapter = browser::FirefoxAdapter::new(firefox_config);
    assert_eq!(firefox_adapter.browser_type(), browser::BrowserType::Firefox);
    
    // 创建浏览器注册表
    let mut registry = browser::BrowserRegistry::new();
    registry.register_adapter(Box::new(chrome_adapter));
    registry.register_adapter(Box::new(firefox_adapter));
    
    // 验证注册
    assert!(registry.get_adapter(&browser::BrowserType::Chrome).is_some());
    assert!(registry.get_adapter(&browser::BrowserType::Firefox).is_some());
    
    println!("✅ 浏览器适配器测试通过");
}

/// 测试消息处理器
#[tokio::test]
async fn test_message_handlers() {
    let mut handler_registry = handlers::HandlerRegistry::new();
    let default_handler = handlers::DefaultMessageHandler::new("default".to_string());
    
    // 注册处理器
    handler_registry.register_handler(
        "default".to_string(), 
        std::sync::Arc::new(default_handler)
    ).await;
    
    // 验证处理器
    let handler = handler_registry.get_handler("default").await;
    assert!(handler.is_some());
    
    println!("✅ 消息处理器测试通过");
}

/// 测试主机注册
#[tokio::test]
async fn test_host_registration() {
    let registration = registry::HostRegistration::new(
        "com.test.secure_password".to_string(),
        "Test Secure Password Manager".to_string(),
        "/test/path/to/host".to_string(),
        vec!["chrome-extension://test-id/".to_string()],
    );
    
    // 验证注册信息
    assert_eq!(registration.name, "com.test.secure_password");
    assert_eq!(registration.description, "Test Secure Password Manager");
    assert_eq!(registration.path, "/test/path/to/host");
    assert_eq!(registration.allowed_origins.len(), 1);
    
    // 创建注册管理器
    let mut registry_manager = registry::RegistryManager::new("/test/daemon/path".to_string());
    registry_manager.create_registrations(vec!["test-extension".to_string()]).unwrap();
    
    // 验证注册配置
    let chrome_registration = registry_manager.get_registration(&browser::BrowserType::Chrome);
    assert!(chrome_registration.is_some());
    
    println!("✅ 主机注册测试通过");
}

/// 测试错误处理
#[tokio::test]
async fn test_error_handling() {
    // 测试协议错误
    let protocol_error = NativeMessagingError::ProtocolError("测试协议错误".to_string());
    let error_message = format!("{}", protocol_error);
    assert!(error_message.contains("协议错误"));
    
    // 测试序列化错误
    let serialization_error = NativeMessagingError::SerializationError("测试序列化错误".to_string());
    let error_message = format!("{}", serialization_error);
    assert!(error_message.contains("序列化错误"));
    
    println!("✅ 错误处理测试通过");
}

/// 测试代理配置
#[tokio::test]
async fn test_proxy_configuration() {
    let proxy_config = ProxyConfig::default();
    assert_eq!(proxy_config.timeout, Duration::from_secs(30));
    
    let ipc_client = IpcClient::new(ClientConfig::default());
    let proxy = RequestProxy::new(proxy_config, ipc_client).await.expect("创建代理失败");
    
    // 验证代理创建成功
    let stats = proxy.get_stats().await;
    assert_eq!(stats.total_requests, 0);
    
    println!("✅ 代理配置测试通过");
}

/// 测试主机配置
#[tokio::test]
async fn test_host_configuration() {
    let host_config = HostConfig::default();
    assert_eq!(host_config.host_name, "com.securepassword.host");
    assert_eq!(host_config.max_concurrent_requests, 10);
    
    println!("✅ 主机配置测试通过");
}

/// 测试协议版本协商
#[tokio::test]
async fn test_protocol_negotiation() {
    let negotiator = protocol::ProtocolNegotiator::new();
    
    // 测试版本协商 - 使用u32版本号
    let client_versions = vec![1u32, 2u32];
    let negotiated_version = negotiator.negotiate(&client_versions);
    assert!(negotiated_version.is_some());
    
    // 测试不兼容版本
    let incompatible_versions = vec![999u32];
    let no_version = negotiator.negotiate(&incompatible_versions);
    assert!(no_version.is_none());
    
    println!("✅ 协议版本协商测试通过");
}

/// 测试消息验证
#[tokio::test]
async fn test_message_validation() {
    let mut message = NativeMessage::new_request(
        "test_request".to_string(),
        json!({"test": "data"}),
        "test_extension".to_string(),
    );
    
    // 测试有效消息
    assert!(message.validate().is_ok());
    
    // 测试无效消息 - 空请求ID
    message.request_id = String::new();
    assert!(message.validate().is_err());
    
    println!("✅ 消息验证测试通过");
}

/// 测试并发处理
#[tokio::test]
async fn test_concurrent_processing() {
    let codec = protocol::ProtocolCodec::new(protocol::ProtocolVersion::V2);
    let mut handles = vec![];
    
    // 创建多个并发任务
    for i in 0..10 {
        let codec_clone = codec.clone();
        let handle = tokio::spawn(async move {
            let message = NativeMessage::new_request(
                format!("concurrent_request_{}", i),
                json!({"test": "data", "index": i}),
                "test_extension".to_string(),
            );
            
            let encoded = codec_clone.encode(&message).expect("编码失败");
            let decoded = codec_clone.decode(&encoded).expect("解码失败");
            
            assert_eq!(decoded.request_id, message.request_id);
        });
        
        handles.push(handle);
    }
    
    // 等待所有任务完成
    for handle in handles {
        handle.await.expect("任务执行失败");
    }
    
    println!("✅ 并发处理测试通过");
}

/// 综合集成测试
#[tokio::test]
async fn test_comprehensive_integration() {
    println!("🚀 开始综合集成测试...");
    
    // 1. 创建协议编解码器
    let codec = protocol::ProtocolCodec::new(protocol::ProtocolVersion::V2);
    
    // 2. 创建测试消息
    let test_message = NativeMessage::new_request(
        "integration_test_001".to_string(),
        json!({
            "action": "get_credentials",
            "domain": "example.com",
            "username": "testuser"
        }),
        "test_extension_id".to_string(),
    );
    
    // 3. 验证消息
    assert!(test_message.validate().is_ok());
    
    // 4. 编解码测试
    let encoded = codec.encode(&test_message).expect("编码失败");
    let decoded = codec.decode(&encoded).expect("解码失败");
    assert_eq!(decoded.request_id, test_message.request_id);
    
    // 5. 创建浏览器适配器
    let chrome_config = browser::BrowserAdapterConfig::default();
    let chrome_adapter = browser::ChromeAdapter::new(chrome_config);
    
    // 6. 创建主机注册
    let registration = registry::HostRegistration::new(
        "com.integration.test".to_string(),
        "Integration Test Host".to_string(),
        "/test/path/to/host".to_string(),
        vec!["chrome-extension://test-id/".to_string()],
    );
    
    // 7. 创建代理
    let proxy_config = ProxyConfig::default();
    let ipc_client = IpcClient::new(ClientConfig::default());
    let proxy = RequestProxy::new(proxy_config, ipc_client).await.expect("创建代理失败");
    
    // 8. 验证所有组件
    assert_eq!(chrome_adapter.browser_type(), browser::BrowserType::Chrome);
    assert_eq!(registration.name, "com.integration.test");
    assert_eq!(proxy.get_stats().await.total_requests, 0);
    
    println!("✅ 综合集成测试通过");
}

/// 性能基准测试
#[tokio::test]
async fn test_performance_benchmark() {
    let codec = protocol::ProtocolCodec::new(protocol::ProtocolVersion::V2);
    let test_message = NativeMessage::new_request(
        "performance_test".to_string(),
        json!({"test": "data"}),
        "test_extension".to_string(),
    );
    
    let start_time = std::time::Instant::now();
    
    // 执行1000次编解码操作
    for _ in 0..1000 {
        let encoded = codec.encode(&test_message).expect("编码失败");
        let _decoded = codec.decode(&encoded).expect("解码失败");
    }
    
    let elapsed = start_time.elapsed();
    println!("1000次编解码操作耗时: {:?}", elapsed);
    
    // 验证性能要求 (应该在1秒内完成)
    assert!(elapsed < Duration::from_secs(1), "性能测试未通过");
    
    println!("✅ 性能基准测试通过");
} 